import React from "react";
import { Metadata } from "next";
import BoxDashboard from "@/components/dashboard/BoxDashboard";
import CurrentPlan from "@/components/dashboard/CurrentPlan";
import Profile from "@/app/(dashboard)/profile/Profile";

export const metadata: Metadata = {
  title: "SEO Analyser | Profile",
  description:
    "Our all-in-one SEO Analyser uncovers hidden issues, delivers actionable insights, and helps you rank higher, faster.",
};

export default function page() {
  return (
    <div className="flex flex-col lg:flex-row items-start gap-3 lg:gap-6 pb-4 w-full">
      <div className="w-full lg:w-full flex flex-col gap-3 lg:gap-6">
        <BoxDashboard>
          <Profile />
        </BoxDashboard>
      </div>
      <BoxDashboard classPlus="lg:mt-0 w-full lg:w-fit flex">
        <CurrentPlan />
      </BoxDashboard>
    </div>
  );
}
