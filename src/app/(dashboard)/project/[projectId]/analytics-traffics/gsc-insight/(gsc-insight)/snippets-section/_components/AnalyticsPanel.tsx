import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/utils/cn";
import Timeline from "./Timeline";
import AveragePositionChart from "./AveragePositionChart";
import TrafficForecastChart from "./TrafficForecastChart";
import { useAveragePositionData } from "../_hooks/useAveragePositionData";
import { useTrafficForecastData } from "../_hooks/useTrafficForecastData";
import { KeywordData } from "../_services/snippetsApi";

const tabButtons = [
  "Average position",
  "Traffic forecast",
  "Keywords overview",
];
const avgPositionBadges = ["Last week", "Month", "3 Month"];

interface AggregatedMetrics {
  totalKeywords: number;
  totalImpressions: number;
  totalClicks: number;
  avgPosition: number;
  ctr: number;
}

interface AnalyticsPanelProps {
  selectedKeyword: string | null;
  keywordData?: KeywordData;
  aggregatedMetrics?: AggregatedMetrics | null;
  totalKeywords?: number;
}

const AnalyticsPanel = ({
  selectedKeyword,
  keywordData,
  aggregatedMetrics,
  totalKeywords = 0,
}: AnalyticsPanelProps) => {
  const [activeTab, setActiveTab] = useState(0);
  const [avgPositionActiveBadge, setAvgPositionActiveBadge] = useState(0);
  const [avgPositionPeriod, setAvgPositionPeriod] = useState("Last week");
  // Traffic forecast now always returns current month; no period selection

  // Fetch average position data
  const {
    data: averagePositionData,
    isLoading: isLoadingAvgPosition,
    error: avgPositionError,
  } = useAveragePositionData(avgPositionPeriod);

  // Fetch traffic forecast data
  const {
    data: trafficForecastData,
    isLoading: isLoadingTrafficForecast,
    error: trafficForecastError,
  } = useTrafficForecastData();

  return (
    <div className="relative space-y-4">
      <div className="flex flex-wrap gap-2">
        {tabButtons.map((item, index) => (
          <button
            key={index}
            onClick={() => setActiveTab(index)}
            className={cn(
              "py-2.5 px-4 text-sm rounded-lg transition-colors duration-300",
              index === activeTab
                ? "bg-[#914AC4]/10 text-[#914AC4]"
                : "text-secondary hover:text-[#914AC4]"
            )}
          >
            {item}
          </button>
        ))}
      </div>

      {/* Date range buttons are now shown directly in each chart component */}

      <div className="w-full min-h-[280px] relative overflow-visible">
        <AnimatePresence mode="wait">
          {activeTab === 0 && (
            <motion.div
              key="averagePosition"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="w-full"
            >
              {isLoadingAvgPosition ? (
                <div className="flex items-center justify-center h-full mt-12">
                  <div className="text-center space-y-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#914AC4] mx-auto"></div>
                    <p className="text-sm text-secondary/70">
                      Loading average position data...
                    </p>
                  </div>
                </div>
              ) : avgPositionError ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-sm text-red-500">
                      Failed to load average position data
                    </p>
                    <p className="text-xs text-secondary/50">
                      Please try again later
                    </p>
                  </div>
                </div>
              ) : averagePositionData?.data?.daily_metrics ? (
                <AveragePositionChart
                  data={averagePositionData.data.daily_metrics}
                  onPeriodChange={(period) => {
                    setAvgPositionPeriod(period);
                    setAvgPositionActiveBadge(
                      avgPositionBadges.indexOf(period)
                    );
                  }}
                  activePeriod={avgPositionPeriod}
                  selectedKeyword={selectedKeyword}
                  keywordData={
                    selectedKeyword &&
                    averagePositionData?.data?.keywords?.[selectedKeyword]
                      ?.daily_metrics
                      ? averagePositionData.data.keywords[selectedKeyword]
                          .daily_metrics
                      : undefined
                  }
                 keywordAvgPosition={
                   selectedKeyword &&
                   averagePositionData?.data?.keywords?.[selectedKeyword]
                     ?.avg_position !== undefined
                     ? averagePositionData.data.keywords[selectedKeyword]
                         .avg_position
                     : undefined
                 }
                  overallAvgPosition={
                    typeof averagePositionData?.data?.totals?.avg_position ===
                    "number"
                      ? averagePositionData.data.totals.avg_position
                      : undefined
                  }
                  showDateRangeButtons={true}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-sm text-secondary/70">
                      No average position data available
                    </p>
                    <p className="text-xs text-secondary/50">
                      Try selecting a different period
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 1 && (
            <motion.div
              key="trafficForecast"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="w-full"
            >
              {isLoadingTrafficForecast ? (
                <div className="flex items-center justify-center h-full mt-12 py-4">
                  <div className="text-center space-y-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#914AC4] mx-auto"></div>
                    <p className="text-sm text-secondary/70">
                      Loading traffic forecast data...
                    </p>
                  </div>
                </div>
              ) : trafficForecastError ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-sm text-red-500">
                      Failed to load traffic forecast data
                    </p>
                    <p className="text-xs text-secondary/50">
                      Please try again later
                    </p>
                  </div>
                </div>
              ) : trafficForecastData?.data?.daily_metrics ? (
                <TrafficForecastChart
                  data={trafficForecastData.data.daily_metrics}
                  period={"Month"}
                  activePeriod={"Month"}
                  selectedKeyword={selectedKeyword}
                  keywordData={
                    selectedKeyword &&
                    trafficForecastData?.data?.keywords?.[selectedKeyword]
                      ?.daily_metrics
                      ? trafficForecastData.data.keywords[selectedKeyword]
                          .daily_metrics
                      : undefined
                  }
                  showDateRangeButtons={false}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-sm text-secondary/70">
                      No traffic forecast data available
                    </p>
                    <p className="text-xs text-secondary/50">
                      Data will be available once generated
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 2 && (
            <motion.div
              key="keywordsOverview"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="space-y-4 p-4"
            >
              {selectedKeyword && keywordData ? (
                // Selected keyword metrics
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.05, duration: 0.2 }}
                >
                  <h3 className="text-sm font-semibold text-secondary">
                    Keyword: "{selectedKeyword}"
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    {[
                      {
                        label: "Position",
                        value: keywordData.avg_position.toFixed(1),
                      },
                      {
                        label: "Impressions",
                        value: keywordData.impressions.toLocaleString(),
                      },
                      {
                        label: "Clicks",
                        value: keywordData.clicks.toLocaleString(),
                      },
                      {
                        label: "URLs",
                        value: keywordData.snippets.length.toString(),
                      },
                    ].map((metric, index) => (
                      <motion.div
                        key={metric.label}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                          delay: 0.1 + index * 0.05,
                          duration: 0.2,
                        }}
                        className="bg-secondary/5 rounded-lg p-3"
                      >
                        <div className="text-xs text-secondary/60 uppercase tracking-wide mb-1">
                          {metric.label}
                        </div>
                        <div className="text-lg font-semibold text-secondary">
                          {metric.value}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ) : aggregatedMetrics ? (
                // Overall metrics
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.05, duration: 0.2 }}
                >
                  <h3 className="text-sm font-semibold text-secondary">
                    Overall Performance
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    {[
                      {
                        label: "Keywords",
                        value: aggregatedMetrics.totalKeywords.toString(),
                      },
                      {
                        label: "Avg Position",
                        value: aggregatedMetrics.avgPosition.toFixed(1),
                      },
                      {
                        label: "Total Impressions",
                        value:
                          aggregatedMetrics.totalImpressions.toLocaleString(),
                      },
                      {
                        label: "Total Clicks",
                        value: aggregatedMetrics.totalClicks.toLocaleString(),
                      },
                      {
                        label: "CTR",
                        value: `${aggregatedMetrics.ctr.toFixed(2)}%`,
                      },
                    ].map((metric, index) => (
                      <motion.div
                        key={metric.label}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                          delay: 0.1 + index * 0.05,
                          duration: 0.2,
                        }}
                        className={cn(
                          "bg-secondary/5 rounded-lg p-3",
                          metric.label === "CTR" && index === 4
                            ? "col-span-2"
                            : ""
                        )}
                      >
                        <div className="text-xs text-secondary/60 uppercase tracking-wide mb-1">
                          {metric.label}
                        </div>
                        <div className="text-lg font-semibold text-secondary">
                          {metric.value}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-2">
                    <p className="text-sm text-secondary/70">
                      No data available
                    </p>
                    <p className="text-xs text-secondary/50">
                      Select a keyword to view details
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AnalyticsPanel;
