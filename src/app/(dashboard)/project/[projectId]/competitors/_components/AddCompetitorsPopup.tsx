"use client";

/* ================================ REACT ================================ */
import React, { useState } from "react";

/* ================================ NEXT.JS ================================ */
import Link from "next/link";
import Image from "next/image";

/* ================================ FRAMER MOTION ================================ */
import { AnimatePresence, motion } from "framer-motion";

/* ================================ ICONS ================================ */
import { HiOutlineGlobeAsiaAustralia } from "react-icons/hi2";
import { LuX } from "react-icons/lu";
import { RiGlobalLine } from "react-icons/ri";

/* ================================ HEADLESS UI ================================ */
import { Input, Switch } from "@headlessui/react";

/* ================================ ASSETS ================================ */
import googleLogo from "@/../public/images/dashboard/google.svg";
import yahooLogo from "@/../public/images/dashboard/yahoo.svg";
import bingLogo from "@/../public/images/dashboard/bing.svg";
import Flag from "react-world-flags";

/* ================================= ZUSTAND ================================ */
import { useCompetitorPopupStore } from "@/store/competitors/useAddCompetitorsPopup";
import { useCompetitorsStore } from "@/store/competitors/useCompetitorsAdded";

/* ================================ TYPES ================================ */
import type { SelectedCompetitors } from "../types/Competitors.type";
type Country = {
  name: string;
  flag: string;
};

type SearchEngine = {
  name: string;
  logo: string;
};

type AddCompetitorsPopupProps = {
  onClose?: () => void;
};

/* ================================ COMPONENTS ================================ */
import { Button } from "@/components/ui/button";
import Card from "@/components/ui/card";
import Dropdown from "@/components/ui/Dropdown";
import Title from "@/components/ui/Title";

/**
 * SelectedCompetitorsItem Component
 *
 * Displays a single selected competitor with their details
 */
const SelectedCompetitorsItem: React.FC<
  SelectedCompetitors & { onRemove: (index: number) => void }
> = ({ country, name, searchEngine, onRemove, index }) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <motion.div
      className="flex items-center justify-between bg-white rounded-sm py-2 px-4 text-secondary/80"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.1 }}
    >
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-4">
          <span className="text-sm ">{`${index + 1}`}</span>
          <span className="text-sm">{name}</span>
        </div>
        <div className="flex items-center gap-4 text-xs text-secondary">
          <div className="bg-[#E0E0E0] h-8 w-10 rounded-full flex justify-center items-center">
            <Image
              src={searchEngine}
              alt={name}
              className="w-[18px] h-[18px]"
            />
          </div>
          <div className="bg-[#E0E0E0] h-8 w-10 rounded-full flex justify-center items-center">
            <Flag code={country} className="w-6 rounded-[2px] " />
          </div>
        </div>
      </div>
      <button
        onClick={() => onRemove(index)}
        className="text-secondary hover:text-red-700 transition-colors"
        aria-label={`Remove ${name}`}
      >
        <LuX className="w-4 h-4" />
      </button>
    </motion.div>
  );
};

/* ================================ MAIN ================================ */

/**
 * AddCompetitorsPopup Component
 *
 * A modal component for adding competitors to a project with search engine
 * and country selection capabilities.
 *
 * @example
 * ```tsx
 * <AddCompetitorsPopup onClose={() => setIsOpen(false)} />
 * ```
 */
const AddCompetitorsPopup: React.FC<AddCompetitorsPopupProps> = () => {
  /* ======================================================================= */
  /*                                   STATES                                */
  /* ======================================================================= */
  const [autofillEnabled, setAutofillEnabled] = useState<boolean>(true);
  const [competitorUrl, setCompetitorUrl] = useState<string>("");
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [selectedSearchEngine, setSelectedSearchEngine] =
    useState<SearchEngine | null>(null);

  /* ======================================================================= */
  /*                                 CONSTANTS                               */
  /* ======================================================================= */

  const countries: Country[] = [
    { name: "United States", flag: "us" },
    { name: "United Kingdom", flag: "gb" },
    { name: "Canada", flag: "ca" },
    { name: "France", flag: "fr" },
    { name: "Germany", flag: "de" },
    { name: "Spain", flag: "es" },
    { name: "Italy", flag: "it" },
    { name: "Australia", flag: "au" },
    { name: "Japan", flag: "jp" },
    { name: "Brazil", flag: "br" },
  ];

  const searchEngines: SearchEngine[] = [
    { name: "Google", logo: googleLogo },
    { name: "Bing", logo: bingLogo },
    { name: "Yahoo", logo: yahooLogo },
  ];

  /* ========================================================================== */
  /*                                    HOOKS                                   */
  /* ========================================================================== */
  const { isVisible, hide } = useCompetitorPopupStore();
  const { addCompetitor, addedCompetitors, removeCompetitor } =
    useCompetitorsStore();

  /* ======================================================================= */
  /*                                 HANDLERS                                */
  /* ======================================================================= */

  /**
   * Handles closing the modal
   */
  const handleClose = (): void => {
    hide();
  };

  /**
   * Handles adding a competitor to the project
   */
  const handleAddCompetitor = (): void => {
    if (!competitorUrl.trim() || !selectedCountry || !selectedSearchEngine)
      return;

    // Add the new competitor to the list
    const newCompetitor: SelectedCompetitors = {
      name: competitorUrl.trim(),
      searchEngine: selectedSearchEngine.logo,
      country: selectedCountry.flag,
      index: addedCompetitors.length,
    };

    addCompetitor(newCompetitor);
    setCompetitorUrl("");
  };

  /**
   * Handles keyboard events for the input field
   */
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === "Enter" && addedCompetitors.length < 5) {
      handleAddCompetitor();
    }
  };

  /**
   * Handles removing a competitor from the list
   */
  const handleRemoveCompetitor = (index: number): void => {
    removeCompetitor(index);
  };

  /* ======================================================================= */
  /*                                   RENDER                                 */
  /* ======================================================================= */

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 bg-black/10 backdrop-blur-[1px] flex justify-center items-center z-50 p-4"
          onClick={handleClose}
          role="dialog"
          aria-modal="true"
          aria-labelledby="competitors-popup-title"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 0.1,
              ease: [0.25, 0.46, 0.45, 0.94], // Custom easing for smooth feel
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <Card className="w-full max-w-4xl space-y-6 p-6">
              {/* =============================== HEADER ============================== */}
              <div className="flex items-center justify-between">
                <div id="competitors-popup-title">
                  <Title className="text-lg">Add Competitors To Project</Title>
                </div>
                <button
                  onClick={handleClose}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Close dialog"
                >
                  <LuX className="text-secondary text-lg" strokeWidth={2} />
                </button>
              </div>

              {/* =============================== DESCRIPTION ============================== */}
              <p className="text-sm text-secondary leading-relaxed">
                You can add up to 5 competitors to a project and track their
                ranking position changes. That way, you'll be able to know the
                results of their promotional activities and compare them with
                that of your site.
              </p>

              {/* =============================== UPGRADE PROMPT ============================== */}
              <div>
                <span className="text-sm text-secondary font-medium">
                  <Link
                    href="/pricing"
                    className="bg-gradient-to-b from-[#B17CD8] to-[#914AC4] bg-clip-text text-transparent hover:text-primary transition-colors duration-200 font-semibold"
                  >
                    Upgrade Plan
                  </Link>{" "}
                  to add more competitors
                </span>
              </div>

              {/* =============================== CONTROLS SECTION ============================== */}
              <div className="bg-[#F4F4F4] rounded-xl p-4">
                <div className="flex flex-col lg:flex-row items-start lg:items-center gap-2 h-[45px]">
                  {/* =============================== DROPDOWNS ============================== */}
                  <div className="w-full flex gap-2">
                    <Dropdown className="w-full h-full">
                      <Dropdown.Button className="bg-white text-xs flex items-center justify-between w-[190px]">
                        {selectedSearchEngine ? (
                          <div className="flex gap-2 items-center">
                            <Image
                              src={selectedSearchEngine.logo}
                              alt={selectedSearchEngine.name}
                              className="w-4 h-4"
                            />
                            <span>{selectedSearchEngine.name}</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <RiGlobalLine className="text-secondary text-lg" />
                            <span className="text-secondary">
                              Select Search Engine
                            </span>
                          </div>
                        )}
                      </Dropdown.Button>
                      <Dropdown.Options className="bg-white border-t-0 border-gray-200 shadow-lg">
                        {searchEngines.map(({ name, logo }, index) => (
                          <Dropdown.Option
                            key={index}
                            onClick={() => {
                              setSelectedSearchEngine({ name, logo });
                            }}
                          >
                            <div className="flex gap-2 items-center">
                              <Image
                                src={logo}
                                alt={name}
                                className="w-4 h-4"
                              />
                              <span>{name}</span>
                            </div>
                          </Dropdown.Option>
                        ))}
                      </Dropdown.Options>
                    </Dropdown>

                    <Dropdown className="w-full h-full">
                      <Dropdown.Button className="bg-white flex items-center justify-between">
                        {selectedCountry ? (
                          <div className="flex gap-2 items-center">
                            <Flag
                              code={selectedCountry.flag}
                              className="w-4 rounded-[2px]"
                            />
                            <span>{selectedCountry.name}</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <HiOutlineGlobeAsiaAustralia className="text-secondary text-lg" />

                            <span className="text-secondary text-xs">
                              Select Countries
                            </span>
                          </div>
                        )}
                      </Dropdown.Button>
                      <Dropdown.Options className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-y-auto h-[200px]">
                        {countries.map((country, index) => (
                          <Dropdown.Option
                            key={index}
                            onClick={() =>
                              setSelectedCountry({
                                name: country.name,
                                flag: country.flag,
                              })
                            }
                          >
                            <div className="flex gap-2 items-center">
                              <Flag
                                code={country.flag}
                                className="w-4 rounded-[2px]"
                              />
                              <span>{country.name}</span>
                            </div>
                          </Dropdown.Option>
                        ))}
                      </Dropdown.Options>
                    </Dropdown>
                  </div>

                  {/* =============================== INPUT SECTION ============================== */}
                  <div className="bg-white rounded-lg flex items-center justify-between w-full h-full px-2">
                    <Input
                      type="text"
                      placeholder="Competitor URL"
                      value={competitorUrl}
                      onChange={(e) => setCompetitorUrl(e.target.value)}
                      onKeyDown={handleKeyPress}
                      className="flex-1 bg-transparent px-3 py-2.5 rounded-l-lg ring-0 focus:ring-0 focus:ring-offset-0 focus:border-none border-none outline-none focus:outline-none text-sm"
                      aria-label="Competitor URL input"
                    />
                    <Button
                      onClick={handleAddCompetitor}
                      disabled={
                        !competitorUrl.trim() ||
                        !selectedCountry ||
                        !selectedSearchEngine ||
                        addedCompetitors.length > 4
                      }
                      className="rounded-sm h-auto px-2 py-1 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Add
                    </Button>
                  </div>

                  {/* =============================== AUTOFILL TOGGLE ============================== */}
                  <div className="flex items-center gap-2">
                    <label
                      htmlFor="autofill-competitors"
                      className="text-sm text-secondary cursor-pointer select-none"
                    >
                      Autofill
                    </label>
                    <Switch
                      id="autofill-competitors"
                      checked={autofillEnabled}
                      onChange={setAutofillEnabled}
                      className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-200 transition data-checked:bg-primary"
                      aria-label="Toggle autofill feature"
                    >
                      <span className="size-3 translate-x-1 rounded-full bg-white transition group-data-checked:translate-x-6" />
                    </Switch>
                  </div>

                  {/* =============================== COUNTER ============================== */}
                  <div className="text-sm text-secondary/80 bg-primary/10 rounded-lg px-3 py-2 font-medium whitespace-nowrap">
                    {addedCompetitors.length} of 5
                  </div>
                </div>

                {/* =============================== SELECTED COMPETITORS ============================== */}
                {addedCompetitors.length > 0 && (
                  <div className="space-y-2 mt-4">
                    <AnimatePresence>
                      {addedCompetitors.map(
                        (competitor: SelectedCompetitors, index: number) => (
                          <SelectedCompetitorsItem
                            index={index}
                            key={`${competitor.name}-${index}`}
                            country={competitor.country}
                            name={competitor.name}
                            searchEngine={competitor.searchEngine}
                            onRemove={handleRemoveCompetitor}
                          />
                        )
                      )}
                    </AnimatePresence>
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AddCompetitorsPopup;
