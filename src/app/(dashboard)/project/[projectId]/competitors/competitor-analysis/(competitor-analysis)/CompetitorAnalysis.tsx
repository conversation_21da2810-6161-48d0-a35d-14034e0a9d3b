"use client";
import React from "react";
import Competitors from "./competitors/Competitors";
import SEOCompetitorLandscape from "./seo-competitor-landscape/SEOCompetitorLandscape";
import AllReportsCompetitorsTable from "./all-reports-competitors-table/AllReportsCompetitorsTable";
import CompetitorAnalysisHeader from "./_components/CompetitorAnalysisHeader";
import NoCompetitorsAdded from "../../_components/NoCompetitorsAdded";
import { useCompetitorsStore } from "@/store/competitors/useCompetitorsAdded";
import { useProjectContext } from "@/contexts/ProjectContext";

const CompetitorAnalysis = () => {
  // TODO: Replace this with backend logic. if no competitors added noData = true else false
  const { addedCompetitors } = useCompetitorsStore();
  const noData = addedCompetitors.length <= 0;
  const { projectName } = useProjectContext();

  return (
    <div className="w-full max-w-full overflow-hidden space-y-6">
      <CompetitorAnalysisHeader
        title={`${projectName} Competitor Analysis`}
        onDateRangeClick={() => {}}
      />
      {noData ? (
        <NoCompetitorsAdded />
      ) : (
        <>
          <Competitors />
          <SEOCompetitorLandscape />
          <AllReportsCompetitorsTable />
        </>
      )}
    </div>
  );
};

export default CompetitorAnalysis;
