import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { BarChartDataRequest } from "./_components/BarChartComponent.types";
import { useProjectId } from "@/hooks/useProjectId";
import { CompetitorApiResponse } from "@/utils/api-response-convertors/lineChartDataConvertor";

/* =========================== GET LINE CHART DATA ========================== */

export const useCompetitorsSection = ({
  period,
  groupBy,
  activeTab,
}: BarChartDataRequest) => {
  const { projectId } = useProjectId();

  return useQuery({
    queryKey: ["useLineChartData", period, projectId, groupBy, activeTab],
    queryFn: async (): Promise<CompetitorApiResponse> => {
      const { data } = await AXIOS.get(
        `/api/projects/${projectId}/competitors/overview/`,
        {
          params: {
            period,
            force_refresh: false,
            group_by: groupBy,
            active_tab: activeTab,
          },
        }
      );
      return data.data;
    },
    enabled: projectId !== null,
  });
};
