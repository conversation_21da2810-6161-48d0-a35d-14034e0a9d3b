"use client";
/* =============================== REACT NEXT =============================== */
import React, { useEffect, useState } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* ================================= ZUSTAND ================================ */
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ================================== HOOKS ================================= */
import { useCompetitorsSection } from "./Competitors.hooks";

/* ================================ CONSTANTS =============================== */
import { periodOptions, groupByOptions, barChartList } from "./constants";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================ CONTEXTS ================================ */
import { useProjectContext } from "@/contexts/ProjectContext";

/* ================================== TYPES ================================= */
import type { LineChartDataResponse } from "../../../market-analysis/(market-analysis)/MarketAnalysis.types";
import { BarChartDataResponse } from "./_components/BarChartComponent.types";

/* ================================== UTILS ================================= */
import { lineChartConvertor } from "@/utils/api-response-convertors/lineChartDataConvertor";
import { barChartDataConvertor } from "@/utils/api-response-convertors/barChartDataConvertor";
import { cardTabsConvertor, TabsType } from "./utils/cardTabsConvertor";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* =============================== COMPONENTS =============================== */
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import GSCLineChart from "@/app/(dashboard)/project/[projectId]/analytics-traffics/analytic-insight/_components/line-chart/LineChart";
import Card from "@/components/ui/card";
import CardTab from "@/components/ui/card-tab/CardTab";
import Dropdown from "@/components/ui/Dropdown";
import Title from "@/components/ui/Title";
import BarChartComponent from "./_components/BarChartComponent";
import LineChartSkeleton from "../../../../analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";

/* ================================== MAIN ================================== */
const Competitors = () => {
  /* ========================================================================== */
  /*                                   STATES                                   */
  /* ========================================================================== */
  const [convertedTabs, setConvertedTabs] = useState<TabsType[]>([
    {
      title: "",
      value: "",
      changeValue: "",
    },
  ]);
  const [period, setPeriod] = useState<string>(periodOptions[0]);
  const [activeTab, setActiveTab] = useState<string>("");
  const [groupBy, setGroupBy] = useState<string>(groupByOptions[0]);
  const { projectName } = useProjectContext();
  const [convertedLineChart, setConvertedLineChart] = useState<
    LineChartDataResponse | undefined
  >(undefined);
  const [convertedBarChart, setConvertedBarChart] = useState<
    BarChartDataResponse | undefined
  >(undefined);

  /* ========================================================================== */
  /*                                    HOOKS                                   */
  /* ========================================================================== */
  const { themeColor } = useProjectThemeColor();
  const { data, isLoading, isPending } = useCompetitorsSection({
    activeTab,
    period,
    groupBy,
  });

  /* ========================================================================== */
  /*                                 USE EFFECTS                                */
  /* ========================================================================== */
  useEffect(() => {
    if (data) {
      setConvertedLineChart(lineChartConvertor(data));

      let modifiedActiveTabTitle = "";

      switch (activeTab) {
        case "DA Score":
          modifiedActiveTabTitle = "da_score";
          break;
        case "Backlinks":
          modifiedActiveTabTitle = "backlinks";
          break;
        case "Ref. Domains":
          modifiedActiveTabTitle = "referring_domains";
          break;
        default:
          modifiedActiveTabTitle = "";
          break;
      }
      setConvertedBarChart(
        barChartDataConvertor(data, modifiedActiveTabTitle, themeColor)
      );
      if (!isEqual(convertedTabs, cardTabsConvertor(data))) {
        setConvertedTabs(cardTabsConvertor(data));
      }
      if (activeTab === "") {
        setActiveTab(cardTabsConvertor(data)[0].title);
      }
    }
  }, [data, activeTab, convertedTabs, themeColor]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="w-full overflow-hidden space-y-4">
      <div className="flex items-center justify-between">
        <Title>{projectName} Competitor</Title>
      </div>
      <DateRange variation="short" />
      <div className="flex gap-2 overflow-x-auto w-full">
        {!convertedTabs[0].title
          ? Array.from({ length: 7 }).map((_, i) => (
              <CardTab
                key={i}
                isLoading
                className={
                  "gap-0 p-2 text-nowrap h-auto flex-grow-0 border-2 border-transparent min-h-[80px]"
                }
              />
            ))
          : convertedTabs.map(({ title, value, changeValue }, index) => (
              <CardTab
                key={index}
                title={title}
                value={value}
                changeValue={changeValue}
                onSelect={() => {
                  setActiveTab(title);
                }}
                className={cn(
                  "gap-0 p-2 text-nowrap h-auto flex-grow-0 border-2 border-transparent min-h-[80px]",
                  activeTab === title && " border-primary"
                )}
              />
            ))}
      </div>
      <div className="flex flex-col gap-4">
        <div className="flex gap-4 items-center">
          <div className="text-[10px] text-secondary flex items-center gap-2">
            <span>Period :</span>
            {periodOptions.map((option) => (
              <span
                key={option}
                onClick={() => setPeriod(option)}
                className={cn("cursor-pointer font-bold")}
                style={period === option ? { color: themeColor } : {}}
              >
                {option}
              </span>
            ))}
          </div>
          <div className="h-[14px] w-[1px] bg-[#E0E0E0]" />
          <div className="text-[10px] text-secondary flex items-center gap-2">
            <span>Group by :</span>
            <Dropdown className="bg-white">
              <Dropdown.Button
                className="bg-white text-[10px]"
                chevronClassName="text-xs"
              >
                {groupBy}
              </Dropdown.Button>
              <Dropdown.Options className="bg-white  text-[10px] shadow-md font-bold">
                {groupByOptions.map((option) => (
                  <Dropdown.Option
                    key={option}
                    onClick={() => setGroupBy(option)}
                    className={cn(
                      "cursor-pointer",
                      groupBy === option && "text-primary"
                    )}
                  >
                    {option}
                  </Dropdown.Option>
                ))}
              </Dropdown.Options>
            </Dropdown>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span
            className="text-[#6C757D] text-sm"
            style={{ writingMode: "vertical-rl", rotate: "180deg" }}
          >
            {activeTab}
          </span>
          <div className="h-[300px] w-full flex justify-center items-center">
            {barChartList.includes(activeTab) ? (
              convertedBarChart ? (
                <BarChartComponent
                  barChartData={convertedBarChart.barChartData}
                  colors={convertedBarChart.colors}
                  cardsData={convertedBarChart.cardsData}
                />
              ) : isLoading || isPending ? (
                <BarChartComponent isLoading />
              ) : null
            ) : (
              <AnimatePresence mode="wait">
                {convertedLineChart ? (
                  <motion.div
                    className="w-full"
                    key="data"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.1 }}
                  >
                    <GSCLineChart
                      lineChartData={convertedLineChart.lineChartData}
                      colors={convertedLineChart.colors}
                      selectedLines={convertedLineChart.selectedLines}
                      cardsData={convertedLineChart.cardsData}
                      tooltipItemsClassName="grid-flow-row"
                    />
                  </motion.div>
                ) : isLoading || isPending ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.1 }}
                  >
                    <LineChartSkeleton />
                  </motion.div>
                ) : (
                  <div key="null">null</div>
                )}
              </AnimatePresence>
            )}
          </div>
        </div>
      </div>
      {!barChartList.includes(activeTab) && (
        <div className="flex gap-4 pt-6">
          {convertedLineChart &&
            convertedLineChart.colors.map(({ name, color }) => (
              <div key={name} className="flex items-center gap-2">
                <div
                  className="w-2 h-2 rounded-[4px]"
                  style={{ backgroundColor: color }}
                />
                <span className="text-xs text-secondary">
                  {name.replace("_", ".")}
                </span>
              </div>
            ))}
        </div>
      )}
    </Card>
  );
};

export default Competitors;
