import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { BubbleChartDataRequest } from "./_components/BubbleChartComponent.types";

import {
  CompetitorDistributionApiResponse,
  ContentCoverageBubbleApiResponse,
  ShareOfVoiceApiResponse,
} from "./utils/seoCompetitorsConvertors";

import { useProjectId } from "@/hooks/useProjectId";

/* ========================== GET BUBBLE CHART DATA ========================= */
export const useBubbleChartData = ({
  selectedBadge,
}: BubbleChartDataRequest) => {
  const { projectId } = useProjectId();

  /* ========================= COMPETITOR DISTRIBUTION ======================== */
  // if (selectedBadge === "Competitor Distribution") {
  //   return useQuery({
  //     queryKey: ["useBubbleChartData", selectedBadge, projectId],
  //     queryFn: async (): Promise<CompetitorDistributionApiResponse> => {
  //       const { data } = await AXIOS.get(
  //         `/api/projects/${projectId}/competitors/distribution/`,
  //         {
  //           params: {
  //             force_refresh: false,
  //           },
  //         }
  //       );
  //       return data;
  //     },
  //   });
  //   /* ============================ CONTENT COVERAGE ============================ */
  // } else {
  //   return useQuery({
  //     queryKey: ["useBubbleChartData", selectedBadge, projectId],
  //     queryFn: async (): Promise<ContentCoverageBubbleApiResponse> => {
  //       const { data } = await AXIOS.get(
  //         `/api/projects/${projectId}/competitors/content-coverage/`,
  //         {
  //           params: {
  //             force_refresh: false,
  //           },
  //         }
  //       );
  //       return data;
  //     },
  //   });
  // }

  return useQuery<
    CompetitorDistributionApiResponse | ContentCoverageBubbleApiResponse
  >({
    queryKey: ["useBubbleChartData", selectedBadge, projectId],
    queryFn:
      selectedBadge === "Competitor Distribution"
        ? async (): Promise<CompetitorDistributionApiResponse> => {
            const { data } = await AXIOS.get(
              `/api/projects/${projectId}/competitors/distribution/`,
              {
                params: {
                  force_refresh: false,
                },
              }
            );
            return data;
          }
        : async (): Promise<ContentCoverageBubbleApiResponse> => {
            const { data } = await AXIOS.get(
              `/api/projects/${projectId}/competitors/content-coverage/`,
              {
                params: {
                  force_refresh: false,
                },
              }
            );
            return data;
          },
  });
};

/* =========================== GET DOUGHNUT CHART =========================== */
export const useDoughnutChartData = ({
  selectedBadge,
}: BubbleChartDataRequest) => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: ["useDoughnutChartData", selectedBadge],
    queryFn: async (): Promise<ShareOfVoiceApiResponse> => {
      const { data } = await AXIOS.get(
        `/api/projects/${projectId}/competitors/share-of-voice/`,
        {
          params: {
            force_refresh: false,
          },
        }
      );
      return data;
    },
  });
};
