"use client";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import SEOCompetitorPieChart from "./_components/SEOCompetitorPieChart";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import React, { useEffect, useState } from "react";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";
import BubbleChartComponent from "./_components/BubbleChartComponent";
import Badge from "@/app/(dashboard)/project/[projectId]/analytics-traffics/analytic-insight/_components/Badge";
import {
  useBubbleChartData,
  useDoughnutChartData,
} from "./SEOCompetitorLandscape.hooks";
import { useProjectContext } from "@/contexts/ProjectContext";
import { BubbleChartDataResponse } from "./_components/BubbleChartComponent.types";
import {
  CompetitorDistributionApiResponse,
  competitorDistributionConvertor,
  ContentCoverageBubbleApiResponse,
  contentCoverageConvertor,
  seoCompetitorPieConvertor,
} from "./utils/seoCompetitorsConvertors";
import { SEOCompetitorPieChartResponse } from "./_components/SEOCompetitorPieChart.type";

const SEOCompetitorLandscape = () => {
  /* ========================================================================== */
  /*                                   STATES                                   */
  /* ========================================================================== */
  const [convertedBubbleChartData, setConvertedBubbleChartData] = useState<
    BubbleChartDataResponse[]
  >([]);
  const [convertedDoughnutChartData, setConvertedDoughnutChartData] = useState<
    SEOCompetitorPieChartResponse | undefined
  >(undefined);

  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const themeColor = useProjectThemeColor();
  const badges = [
    "Competitor Distribution",
    "Content Coverage Bubble",
    "Share of Voice",
  ];
  const [selectedBadge, setSelectedBadge] = useState(badges[0]);
  const { projectName } = useProjectContext();

  const {
    data: bubbleChartData,
    isLoading: bubbleChartLoading,
    isPending: bubbleChartPending,
  } = useBubbleChartData({ selectedBadge });
  const {
    data: doughnutChartData,
    isLoading: doughnutChartLoading,
    isPending: doughnutChartPending,
  } = useDoughnutChartData({ selectedBadge });

  /* ========================================================================== */
  /*                                 USE EFFECTS                                */
  /* ========================================================================== */
  useEffect(() => {
    if (!bubbleChartData) return;

    function isCompetitorDistributionApiResponse(
      data:
        | CompetitorDistributionApiResponse
        | ContentCoverageBubbleApiResponse
        | SEOCompetitorPieChartResponse
    ): data is CompetitorDistributionApiResponse {
      return "results" in data;
    }

    function isContentCoverageBubbleApiResponse(
      data:
        | CompetitorDistributionApiResponse
        | ContentCoverageBubbleApiResponse
        | SEOCompetitorPieChartResponse
    ): data is ContentCoverageBubbleApiResponse {
      return "contentCoverage" in data;
    }

    if (isCompetitorDistributionApiResponse(bubbleChartData)) {
      setConvertedBubbleChartData(
        competitorDistributionConvertor(bubbleChartData)
      );
    } else if (isContentCoverageBubbleApiResponse(bubbleChartData)) {
      setConvertedBubbleChartData(contentCoverageConvertor(bubbleChartData));
    }

    if (doughnutChartData) {
      setConvertedDoughnutChartData(
        seoCompetitorPieConvertor(doughnutChartData)
      );
    }
  }, [bubbleChartData, doughnutChartData]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="w-full overflow-hidden space-y-6">
      <div className=" space-y-4">
        <Title>{projectName} SEO Competitor Landscape</Title>
        <DateRange variation="short" />
      </div>
      <div className="flex gap-4">
        {badges.map((badge, index) => (
          <Badge
            selected={selectedBadge === badge}
            key={index}
            onSelect={() => setSelectedBadge(badge)}
            style={
              selectedBadge === badge
                ? {
                    backgroundColor: themeColor + "10",
                    color: themeColor as unknown as string,
                  }
                : {}
            }
          >
            {badge}
          </Badge>
        ))}
      </div>
      {selectedBadge === "Share of Voice" ? (
        doughnutChartPending || doughnutChartLoading ? (
          <SEOCompetitorPieChart isLoading />
        ) : (
          convertedDoughnutChartData && (
            <SEOCompetitorPieChart domains={convertedDoughnutChartData} />
          )
        )
      ) : (
        <div className="flex flex-col gap-4 justify-center items-center">
          <div className="h-[300px] w-full flex justify-start items-center">
            <span
              className="text-[#6C757D] text-sm"
              style={{ writingMode: "vertical-rl", rotate: "180deg" }}
            >
              {selectedBadge === "Competitor Distribution"
                ? "Keyword Ranked"
                : "Share Of SERP Keywords"}
            </span>
            {bubbleChartLoading || bubbleChartPending ? (
              <BubbleChartComponent isLoading />
            ) : !!convertedBubbleChartData ? (
              <BubbleChartComponent data={convertedBubbleChartData} />
            ) : null}
          </div>
          <span className="text-[#6C757D] text-sm">
            {selectedBadge === "Competitor Distribution"
              ? "AVG. Position"
              : "Topical Authority Score"}
          </span>
          <div className="flex gap-4 pt-6 w-full">
            {convertedBubbleChartData?.map(({ title, color }) => (
              <div key={title} className="flex items-center gap-2">
                <div
                  className="w-2.5 h-2.5 rounded-[4px]"
                  style={{ backgroundColor: color }}
                />
                <span className="text-xs text-secondary">
                  {title.replace("_", ".")}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );
};

export default SEOCompetitorLandscape;
