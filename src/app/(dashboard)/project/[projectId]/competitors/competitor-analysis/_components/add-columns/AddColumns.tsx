import { Button } from "@/components/ui/button";
import Card from "@/components/ui/card";
import Checkbox from "@/components/ui/Checkbox";
import Capitalize from "@/utils/capitalize";
import { AnimatePresence } from "framer-motion";
import React, { useState } from "react";
import { GoColumns } from "react-icons/go";
import { useClickOutside } from "@mantine/hooks";
import { cn } from "@/utils/cn";

const AddColumns = ({
  columns,
  checkedColumns,
  setCheckedColumns,
  sendColumns,
  isDisabled,
  title,
  icon,
  className,
  classNames,
}: {
  columns: string[];
  checkedColumns: string[];
  setCheckedColumns: (columns: string[]) => void;
  sendColumns: (columns: string[]) => void;
  isDisabled: boolean;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  classNames?: {
    buttonContainer?: string;
    options?: string;
    optionsContainer?: string;
    optionsTitle?: string;
  };
}) => {
  /* ========================================================================== */
  /*                                  STATES                                    */
  /* ========================================================================== */
  const [isOpen, setIsOpen] = useState(false);
  // Attach useClickOutside to a parent div that always exists
  const parentRef = useClickOutside(() => setIsOpen(false));

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div ref={parentRef} style={{ position: "relative" }}>
      <Button
        variant={"outline"}
        className={cn(
          "border-1 bg-transparent rounded-md border-secondary text-secondary hover:bg-secondary hover:text-white px-2 py-1",
          className
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        {icon ?? <GoColumns strokeWidth={0.6} />}
        <span>{title || "Columns"}</span>
      </Button>
      <AnimatePresence>
        {isOpen && (
          <Card
            initial={{
              opacity: 0,
            }}
            animate={{
              opacity: 1,
            }}
            exit={{
              opacity: 0,
            }}
            transition={{
              duration: 0.1,
            }}
            className={cn(
              "absolute top-[40px] right-0 w-[283px] border shadow-md bg-white z-50 px-0 py-2 text-secondary rounded-[8px]",
              classNames?.optionsContainer
            )}
          >
            <div className="px-2 pb-2 border-b-2">
              <span className={classNames?.optionsTitle}>
                {title || "Add Columns"}
              </span>
            </div>
            {columns.map((column) => (
              <div
                key={column}
                className="flex items-center gap-4 px-3 py-2 border-b-2"
              >
                <Checkbox
                  isDisabled={isDisabled}
                  checked={checkedColumns.includes(column)}
                  onChange={() => {
                    if (checkedColumns.includes(column)) {
                      setCheckedColumns(
                        checkedColumns.filter((c) => c !== column)
                      );
                    } else {
                      setCheckedColumns([...checkedColumns, column]);
                    }
                  }}
                />
                <span className={classNames?.options}>
                  {Capitalize(column)}
                </span>
              </div>
            ))}
            <div
              className={cn(
                "pt-8 pb-2 w-full flex justify-center",
                classNames?.buttonContainer
              )}
            >
              <Button
                onClick={() => {
                  setIsOpen(false);
                  sendColumns(checkedColumns);
                }}
                variant={"outline"}
                className="border-primary font-semibold text-primary hover:bg-primary hover:text-white py-1 px-2 h-fit text-xs"
              >
                Done
              </Button>
            </div>
          </Card>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AddColumns;
