import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axiosDev";
import { TableData } from "../../../../analytics-traffics/_components/data-table/DataTable.types";
import { useProjectId } from "@/hooks/useProjectId";

export const useMarketAnalysisTable = (
  columns: string,
  currentPage: number
) => {
  const {projectId} = useProjectId();

  return useQuery({
    queryKey: ["useMarketAnalysisTable", columns, currentPage],
    queryFn: async (): Promise<TableData> => {
      const { data } = await AXIOS.get(
        "api/dashboard/project/competitors/market-analysis/market-analysis-table",
        {
          params: {
            columns,
          },
        }
      );
      return data;
    },
  });
};
