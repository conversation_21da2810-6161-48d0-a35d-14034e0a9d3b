"use client";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import React, { useEffect, useState } from "react";
import DateRange from "../../../../analytics-traffics/_components/date-range/DateRange";
import DataTable from "../../../../analytics-traffics/_components/data-table/DataTable";
import AddColumns from "../../../competitor-analysis/_components/add-columns/AddColumns";
import { Button } from "@/components/ui/button";
import { FiBarChart2 } from "react-icons/fi";
import { useMarketAnalysisTable } from "./AllReportsVisibilityRating.hooks";
import TableSkeleton from "../../../../analytics-traffics/_components/data-table/TableSkeleton";
import Pagination from "../../../../analytics-traffics/_components/Pagination";
import { useSearchVisibilityCharts } from "../search-visibility/SearchVisibility.hooks";
import { useProjectContext } from "@/contexts/ProjectContext";
/* ================================== MAIN ================================== */
const AllReportsVisibilityRating = ({
  selectedChart,
  selectedTableItems,
  setSelectedTableItems,
  selectedExtraColumn,
  setSelectedExtraColumn,
  refetch,
}: {
  selectedChart: string;
  selectedTableItems: string[];
  setSelectedTableItems: React.Dispatch<React.SetStateAction<string[]>>;
  selectedExtraColumn: string[];
  setSelectedExtraColumn: React.Dispatch<React.SetStateAction<string[]>>;
  refetch: () => void;
}) => {
  /* ========================================================================== */
  /*                                   STATES                                   */
  /* ========================================================================== */
  const [currentPage, setCurrentPage] = useState(1);
  const columnsParam =
    selectedExtraColumn.length > 0 ? selectedExtraColumn.join(",") : "";

  const { data, isLoading, isPending, isFetching, isError, error } =
    useMarketAnalysisTable(columnsParam, currentPage);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (data?.totalPages && data.totalPages !== totalPages) {
      setTotalPages(data.totalPages);
    }
  }, [data?.totalPages, totalPages]);

  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { projectName } = useProjectContext();

  if (data) {
    console.log(data);
  }
  const columns = ["% In Top 10", "Domain Trust"];
  if (isError) console.error(error);
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="space-y-4">
      <Title>{projectName} All reports-Visibility Rating</Title>
      <DateRange variation="short" />
      <div className="flex justify-between items-center">
        <Button
          onClick={refetch}
          disabled={
            selectedTableItems.length < 1 ||
            selectedChart !== "Search Visibility"
          }
          variant="outline"
          className="gap-4 text-[#344054]/70 border-[#344054]/70 px-2 py-2.5 h-auto"
        >
          <div
            className="h-5 w-5 border-2 border-[#344054]/70"
            style={{
              borderRadius: "7px",
            }}
          >
            <FiBarChart2 className="scale-x-[-1]" />
          </div>
          <span>compare</span>
        </Button>
        <AddColumns
          columns={columns}
          checkedColumns={selectedExtraColumn}
          setCheckedColumns={setSelectedExtraColumn}
          sendColumns={() => void 0}
          isDisabled={isLoading || isPending || isFetching}
        />
      </div>
      {isLoading || isPending || isFetching ? (
        <TableSkeleton />
      ) : data ? (
        <DataTable
          showDateRange={false}
          checkbox
          tableData={data}
          selectedItems={selectedTableItems}
          setSelectedItems={setSelectedTableItems}
        />
      ) : null}

      <Pagination
        totalPages={totalPages}
        page={currentPage}
        onPageChange={setCurrentPage}
      />
    </Card>
  );
};

export default AllReportsVisibilityRating;
