import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { MarketAnalysisResponse } from "./SearchVisibility.types";
import type { CompetitorApiResponse } from "@/utils/api-response-convertors/lineChartDataConvertor";

import { useProjectId } from "@/hooks/useProjectId";

export const useSearchVisibilityCharts = (
  period: string,
  groupBy: string,
  selectedChart: string,
  activeCard: string,
  selectedTableItems: string,
  selectedCountry: string
) => {
  const {projectId} = useProjectId();
  return useQuery({
    queryKey: [
      "useSearchVisibilityCharts",
      groupBy,
      period,
      selectedChart,
      activeCard,
      selectedCountry,
    ],
    queryFn: async (): Promise<CompetitorApiResponse> => {
      const { data } = await AXIOS.get(
        `/api/projects/${projectId}/competitors/search-visibility`,
        {
          params: {
            // groupBy,
            period,
            // selectedChart,
            // activeCard,
            // selectedTableItems,
            // selectedCountry,
            force_refresh:false
          },
        }
      );
      return data;
    },
    enabled: !!projectId,
    refetchOnWindowFocus: false,
  });
};
