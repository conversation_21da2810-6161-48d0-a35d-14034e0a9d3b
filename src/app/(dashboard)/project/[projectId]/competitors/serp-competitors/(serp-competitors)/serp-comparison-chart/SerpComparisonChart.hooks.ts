import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axiosDev";
import {
  SerpChartResponse,
  SerpChartRequest,
} from "./SerpComparisonChart.types";

import { CHART_PLACEHOLDER_DATA } from "./constants";

export const useSerpComparisonChart = ({
  selectedTableItems,
  selectedDays,
  selectedCountry,
  selectedKeyword,
  fetchIsAllowed,
}: SerpChartRequest & { fetchIsAllowed: boolean }) => {
  return useQuery({
    queryKey: ["SerpComparisonChart"],
    queryFn: async (): Promise<SerpChartResponse> => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/competitors/serp-competitors/serp-competitors-chart",
        {
          params: {
            selectedTableItems,
            selectedDays,
            selectedCountry,
            selectedKeyword,
          },
        }
      );
      return data;
    },
    enabled: fetchIsAllowed,
    placeholderData: () => CHART_PLACEHOLDER_DATA,
  });
};
