import React, { useEffect, useState } from "react";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import SerpLine<PERSON><PERSON> from "./SerpLineChart";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import { FiBarChart2 } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { Payload } from "../../../types/serpTable.types";
import { useSerpComparisonChart } from "./SerpComparisonChart.hooks";
import { SerpChartRequest } from "./SerpComparisonChart.types";
import { useProjectContext } from "@/contexts/ProjectContext";

const SerpComparisonChart = ({
  selectedDate,
  serpTableData,
}: {
  selectedDate: Date[];
  serpTableData?: Payload;
}) => {
  const [fetchIsAllowed, setFetchIsAllowed] = useState(false);
  const [compareButtonEnabled, setCompareButtonEnabled] = useState(false);
  const { projectName } = useProjectContext();

  const {
    selectedDays = [],
    selectedTableItems = [],
    selectedCountry = "",
    selectedKeyword = "",
  } = serpTableData || {};

  const serpChartRequest: SerpChartRequest & { fetchIsAllowed: boolean } = {
    selectedDays: selectedDays.join(","),
    selectedTableItems: selectedTableItems.join(","),
    selectedCountry,
    selectedKeyword,
    fetchIsAllowed,
  };

  const { data, isError, error, isLoading, isPending, isFetched } =
    useSerpComparisonChart(serpChartRequest);

  const loading = isLoading || isPending;

  // 🔄 Enable button when filters change
  useEffect(() => {
    setCompareButtonEnabled(
      selectedDate.length > 0 && selectedTableItems.length > 0
    );
  }, [selectedDate, selectedTableItems]);

  // ❌ Disable button after fetch completes
  useEffect(() => {
    if (isFetched && fetchIsAllowed) {
      setCompareButtonEnabled(false);
      setFetchIsAllowed(false); // reset flag to prevent auto-refetch
    }
  }, [isFetched, fetchIsAllowed]);

  if (isError) console.error(error);

  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center">
        <Title>{projectName} Comparison Chart</Title>
        <Button
          onClick={() => setFetchIsAllowed(true)}
          disabled={loading || !compareButtonEnabled}
          variant="outline"
          className="gap-4 text-[#344054]/70 border-[#344054]/70 px-2 py-2.5 h-auto"
        >
          <div
            className="h-5 w-5 border-2 border-[#344054]/70"
            style={{ borderRadius: "7px" }}
          >
            <FiBarChart2 className="scale-x-[-1]" />
          </div>
          {loading ? <span>loading...</span> : <span>compare</span>}
        </Button>
      </div>
      <DateRange variation="range" />
      <div className="flex items-center justify-around px-4">
        <span
          className="text-[#6C757D] text-sm font-semibold"
          style={{ writingMode: "vertical-rl", rotate: "180deg" }}
        >
          ranking
        </span>
        {!loading && data && (
          <SerpLineChart colors={data.colors} chartData={data.chartData} />
        )}
      </div>
    </Card>
  );
};

export default SerpComparisonChart;
