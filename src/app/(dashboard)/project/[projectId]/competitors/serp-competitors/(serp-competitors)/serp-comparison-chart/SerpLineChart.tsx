import React from "react";
import {
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
} from "recharts";
import type { SerpChartResponse } from "./SerpComparisonChart.types";

const SerpLineChart = ({ colors, chartData }: SerpChartResponse) => {
  const lineKeys = Object.keys(colors);
  if (!colors || !chartData) return null;
  return (
    <div style={{ width: "100%", height: 400 }} className="pt-4">
      <ResponsiveContainer width="100%" height="85%">
        <LineChart data={chartData}>
          <CartesianGrid stroke="#EAEAEA" strokeDasharray="0" />
          <XAxis
            dataKey="name"
            tick={{ fontSize: 12, fill: "#6C757D", fontWeight: 700 }}
            tickLine={false}
            tickMargin={16}
            axisLine={{ stroke: "#EAEAEA" }}
          />
          <YAxis
            tick={{ fontSize: 12, fill: "#6C757D", fontWeight: 700 }}
            tickLine={false}
            tickMargin={16}
            axisLine={{ stroke: "#EAEAEA" }}
          />
          {lineKeys.map((key) => (
            <Line
              key={key}
              type="linear"
              dataKey={key}
              stroke={colors[key]}
              strokeDasharray={colors[key] === "#E0E0E0" ? "4 4" : "0 0"}
              strokeWidth={2}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      <div className="flex gap-4 pt-6">
        {lineKeys.map((key) => (
          <div key={key} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-[5px]"
              style={{ backgroundColor: colors[key] }}
            />
            <span className="text-xs text-secondary">{key}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SerpLineChart;
