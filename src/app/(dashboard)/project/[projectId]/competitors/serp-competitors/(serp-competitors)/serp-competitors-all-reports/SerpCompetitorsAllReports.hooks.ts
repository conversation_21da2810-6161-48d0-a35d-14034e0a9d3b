import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { useProjectId } from "@/hooks/useProjectId";

export const useSerpCompetitorsAllReports = () => {
  const { projectId } = useProjectId();

  return useQuery({
    queryKey: ["competitorsTableData", projectId],
    queryFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const {data} = await AXIOS.get(
        `/api/projects/${projectId}/competitors/visibility-rating`,
        {
          params: {
            force_refresh: false,
          },
        }
      );
      return data.results;
    },
    enabled: projectId !== null,
  });
};
