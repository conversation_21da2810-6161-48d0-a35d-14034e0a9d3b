import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axiosDev";

export const useShareOfVoiceOverview = (
  selectedKeyword: string,
  selectedCountry: string
) => {
  return useQuery({
    queryKey: ["useShareOfVoiceOverview", selectedKeyword, selectedCountry],
    queryFn: async () => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/competitors/share-of-voice/share-of-voice-overview",
        { params: { selectedKeyword, selectedCountry } }
      );
      return data;
    },
  });
};
