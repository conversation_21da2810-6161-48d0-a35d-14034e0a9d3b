/* =============================== REACT NEXT =============================== */
import React, { useState } from "react";
import Image from "next/image";

/* ================================== ICONS ================================= */
import Flag from "react-world-flags";

/* ================================ CONSTANTS =============================== */
import {
  countriesDropdown,
  keywordsDropdown,
} from "../../../_constants/CountriesDownConstants";

/* =============================== COMPONENTS =============================== */
import Dropdown from "@/components/ui/Dropdown";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import LineChartCard from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/_components/LineChartCard";
import SmallChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================ FRAMER MOTION ============================= */
import { AnimatePresence, motion } from "framer-motion";

/* ================================ TYPES ================================ */
import { ChartResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/AudienceOverview.types";
import { LineChartData } from "@/types/LineChartCard.type";
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";
import { useShareOfVoiceOverview } from "./ShareOfVoiceOverview.hooks";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
/*                                 MAIN COMPONENT                             */
/* ========================================================================== */

/**
 * ShareOfVoiceOverview - Displays an overview of share of voice metrics with interactive charts
 *
 * Features:
 * - Shows multiple chart cards with traffic and share of voice data
 * - Interactive dropdowns for keyword and country selection
 * - Animated chart transitions using Framer Motion
 * - Responsive grid layout with proper column spanning
 *
 * @component
 * @example
 * ```tsx
 * <ShareOfVoiceOverview />
 * ```
 */
const ShareOfVoiceOverview = () => {
  /* ========================================================================== */
  /*                                  STATES                                   */
  /* ========================================================================== */
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const [selectedCountry, setSelectedCountry] = useState(countriesDropdown[0]);
  const [selectedKeyword, setSelectedKeyword] = useState(keywordsDropdown[0]);
  const { show } = useChartPopupStore();
  const { projectName } = useProjectContext();

  const { data, isLoading, isPending } = useShareOfVoiceOverview(
    selectedCountry.code,
    selectedKeyword
  );

  /* ========================================================================== */
  /*                                FUNCTIONS                                  */
  /* ========================================================================== */

  /**
   * Handles chart click to show detailed popup
   * @param chart - The chart data to display
   */
  const handleSetChartData = (chart: LineChartData) => {
    setChartData(chart);
    show();
  };

  /**
   * Determines the column span class for responsive grid layout
   * @param index - Current chart index
   * @param totalCharts - Total number of charts
   * @returns CSS class for column spanning
   */
  const getColumnSpanClass = (index: number, totalCharts: number) => {
    const isLastTwoInRowOfFour =
      totalCharts % 4 === 2 && index >= totalCharts - 2;
    return isLastTwoInRowOfFour ? "lg:col-span-2" : "col-span-1";
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <Card className="space-y-8">
      {/* =============================== HEADER SECTION ============================== */}
      <div className="space-y-2">
        <Title>{projectName} Overview Of Share Of Voice</Title>
        <DateRange variation="short" />
      </div>

      {/* =============================== FILTER CONTROLS ============================== */}
      <div className="flex items-center gap-2 h-[40px]">
        <Dropdown className="bg-white h-full">
          <Dropdown.Button className="bg-white border pl-1 pr-4 w-48 h-full">
            <div className="flex items-center gap-2">
              <div className="border p-1 rounded-sm">
                <Image
                  className="h-5"
                  src={selectedCountry.engine}
                  alt="search engine logo"
                  width={20}
                  height={20}
                />
              </div>
              <Flag className="w-5 rounded-xs" code={selectedCountry.code} />
              <span>{selectedCountry.value}</span>
            </div>
          </Dropdown.Button>
          <Dropdown.Options className="border border-t-0">
            {countriesDropdown.map((country) => (
              <Dropdown.Option
                key={country.code}
                onClick={() => setSelectedCountry(country)}
                className="py-1 bg-white flex items-center gap-5 border-t-0 pl-1 pr-4 h-fit"
              >
                <div className="flex items-center gap-2">
                  <div className="border p-1 rounded-sm">
                    <Image
                      className="h-5"
                      src={country.engine}
                      alt="search engine logo"
                      width={20}
                      height={20}
                    />
                  </div>
                  <Flag className="w-5 rounded-xs" code={country.code} />
                  <span>{country.value}</span>
                </div>
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
        <Dropdown className="h-full">
          <Dropdown.Button className="bg-white border h-full px-4 w-48">
            <span>{selectedKeyword}</span>
          </Dropdown.Button>
          <Dropdown.Options>
            {keywordsDropdown.map((keyword) => (
              <Dropdown.Option
                className="bg-white border border-t-0"
                key={keyword}
                onClick={() => setSelectedKeyword(keyword)}
              >
                {keyword}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>

      {/* =============================== CHARTS GRID ============================== */}
      <div className="flex gap-4">
        {isLoading || isPending ? (
          Array.from({ length: 4 }).map((_, i) => (
            <motion.div
              className="w-full"
              key={`loading-${i}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <SmallChartSkeleton className="w-full" />
            </motion.div>
          ))
        ) : (
          <AnimatePresence>
            {data &&
              Array.isArray(data) &&
              data?.map((chart: ChartResponse, index: number) => {
                const colSpan = getColumnSpanClass(index, data.length);

                return (
                  <motion.div
                    key={chart.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className={`w-full ${colSpan}`}
                  >
                    <LineChartCard
                      title={chart.title}
                      className="w-full cursor-pointer h-full"
                      bigNumber={chart.bigNumber}
                      smallNumber={chart.smallNumber}
                      data={chart.data}
                      onClick={() => handleSetChartData(chart)}
                    />
                  </motion.div>
                );
              })}
          </AnimatePresence>
        )}
      </div>
    </Card>
  );
};

export default ShareOfVoiceOverview;
