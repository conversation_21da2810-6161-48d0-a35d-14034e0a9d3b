import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axiosDev";
import type { AccordionTableData } from "../../../_components/AccordionTable";

export const useShareOfVoiceTable = () => {
  return useQuery({
    queryKey: ["useShareOfVoiceTable"],
    queryFn: async (): Promise<AccordionTableData> => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/competitors/share-of-voice/share-of-voice-table"
      );
      return data;
    },
  });
};
