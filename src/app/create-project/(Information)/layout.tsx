"use client";
import SidebarCreateProject from "@/components/CreateProject/SidebarCreateProject";
import React, { useEffect, useRef, Suspense } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { ToastProvider } from "@/lib/ToastProvider";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import { projectAPI } from "@/services/projectService";

// Component that uses useSearchParams - needs to be wrapped in Suspense
function LayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const {
    projectInfo,
    searchEngineConfigs,
    keywords,
    canAccessStep,
    stepCompletion,
    currentStep,
    loadExistingProject,
  } = useCreateProjectStore();
  const { isEditMode, isLoadingProject, projectError } = useEditProject();
  const { urls } = useEditNavigation();

  // If arriving via OAuth callback with pid, fetch project and hydrate store like edit mode
  useEffect(() => {
    const pid = searchParams?.get("pid");
    if (!pid) return;

    // If we're already in edit mode for this project, no need to fetch
    if (isEditMode && projectInfo?.id === pid) return;

    // If store has different project or none, fetch and load
    (async () => {
      try {
        const resp = await projectAPI.getProject(pid);
        await loadExistingProject(resp.data);
      } catch (e) {
        console.warn("Failed to hydrate project from pid:", e);
      }
    })();
  }, [searchParams, isEditMode, projectInfo?.id, loadExistingProject]);

  // Fixed sidebar positioning - maintains position during scroll
  useEffect(() => {
    const sidebar = sidebarRef.current;
    if (!sidebar) return;

    const setFixedPosition = () => {
      const container = sidebar.parentElement;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();
      const containerStyles = window.getComputedStyle(container);
      const paddingLeft = parseInt(containerStyles.paddingLeft);

      // Fixed positioning to prevent movement during scroll
      sidebar.style.position = 'fixed';
      sidebar.style.top = '48px';
      sidebar.style.left = `${containerRect.left + paddingLeft}px`;
      sidebar.style.zIndex = '50';

      // Responsive width calculation
      const isXL = window.innerWidth >= 1280;
      sidebar.style.width = isXL ? '384px' : '320px';
      sidebar.style.height = 'auto';
    };

    setFixedPosition();

    const handleResize = () => setFixedPosition();
    window.addEventListener('resize', handleResize, { passive: true });
    window.addEventListener('scroll', setFixedPosition, { passive: true });

    // Prevent position changes from other scripts
    const observer = new MutationObserver(() => {
      if (sidebar.style.position !== 'fixed') {
        setFixedPosition();
      }
    });

    observer.observe(sidebar, {
      attributes: true,
      attributeFilter: ['style']
    });

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', setFixedPosition);
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    // Skip navigation validation entirely in edit mode or while loading project data
    if (isEditMode || isLoadingProject) {
      return;
    }

    // Always allow access to Analytics Services step (OAuth return landing)
    // This page must not be blocked by step guards in create mode
    if (pathname && pathname.includes("/create-project/analytics-services")) {
      return;
    }

    // Enhanced navigation validation using the store's canAccessStep method
    const isStepAccessible = (stepUrl: string): boolean => {
      // Use the store's enhanced validation logic
      return canAccessStep(stepUrl);
    };

    // Only perform navigation validation in create mode (excluding analytics-services)
    if (pathname && !isStepAccessible(pathname)) {
      // Use enhanced redirect logic based on step completion with proper URL building
      if (!canAccessStep("/create-project/search-engines")) {
        router.replace(urls.projectInformation);
      } else if (!canAccessStep("/create-project/keywords")) {
        router.replace(urls.searchEngines);
      } else if (!canAccessStep("/create-project/competitors")) {
        router.replace(urls.keywords);
      } else if (!canAccessStep("/create-project/analytics-services")) {
        router.replace(urls.competitors);
      }
    }
  }, [
    pathname,
    projectInfo,
    searchEngineConfigs,
    keywords,
    router,
    isEditMode,
    isLoadingProject,
    canAccessStep,
    urls,
    stepCompletion,
    currentStep,
  ]);

  // Show loading state while fetching project data in edit mode
  if (isEditMode && isLoadingProject) {
    return <LayoutLoading />;
  }

  // Show error state if project failed to load
  if (isEditMode && projectError) {
    return (
      <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-lg font-semibold">
            Failed to load project data
          </div>
          <div className="text-gray-600">
            Please try refreshing the page or go back to the projects list.
          </div>
          <button
            onClick={() => router.push("/dashboard/my-projects")}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Back to Projects
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Fixed sidebar - positioned outside main container */}
      <div
        ref={sidebarRef}
        className="hidden lg:block lg:w-80 xl:w-96"
      >
        <SidebarCreateProject />
      </div>

      {/* Main content wrapper with max-width and centering */}
      <div className="w-full max-w-8xl mx-auto lg:ml-[336px] xl:ml-[416px]">
        <div className="px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 min-h-screen lg:mr-4 xl:mr-2">
          {children}
        </div>
      </div>
      <ToastProvider />
    </>
  );
}

// Loading fallback component
function LayoutLoading() {
  return (
    <>
      {/* Sidebar loading skeleton - positioned outside main container */}
      <div className="hidden lg:block lg:w-80 xl:w-96">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 animate-pulse">
          {/* Logo skeleton */}
          <div className="flex justify-center mb-8">
            <div className="w-[140px] h-[55px] bg-gray-200 rounded-lg"></div>
          </div>

          {/* Project name skeleton */}
          <div className="mb-8 text-center">
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-24 mx-auto"></div>
          </div>

          {/* Navigation steps skeleton */}
          <div className="space-y-2 mb-8">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-3 px-4 py-3 rounded-xl">
                <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Progress skeleton */}
          <div className="pt-6 border-t border-gray-100">
            <div className="flex justify-between mb-2">
              <div className="h-3 bg-gray-200 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded w-8"></div>
            </div>
            <div className="w-full bg-gray-100 rounded-full h-2">
              <div className="bg-gray-200 h-2 rounded-full w-1/3"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Content area loading skeleton with max-width and centering */}
      <div className="w-full max-w-8xl mx-auto lg:ml-[336px] xl:ml-[416px]">
        <div className="px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 min-h-screen lg:mr-4 xl:mr-2">
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 animate-pulse">
            <div className="space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<LayoutLoading />}>
      <LayoutContent>{children}</LayoutContent>
    </Suspense>
  );
}
