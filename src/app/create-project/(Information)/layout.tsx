"use client";
import SidebarCreateProject from "@/components/CreateProject/SidebarCreateProject";
import React, { useEffect, useRef, Suspense } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { ToastProvider } from "@/lib/ToastProvider";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import { projectAPI } from "@/services/projectService";

// Component that uses useSearchParams - needs to be wrapped in Suspense
function LayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const {
    projectInfo,
    searchEngineConfigs,
    keywords,
    canAccessStep,
    stepCompletion,
    currentStep,
    loadExistingProject,
  } = useCreateProjectStore();
  const { isEditMode, isLoadingProject, projectError } = useEditProject();
  const { urls } = useEditNavigation();

  // If arriving via OAuth callback with pid, fetch project and hydrate store like edit mode
  useEffect(() => {
    const pid = searchParams?.get("pid");
    if (!pid) return;

    // If we're already in edit mode for this project, no need to fetch
    if (isEditMode && projectInfo?.id === pid) return;

    // If store has different project or none, fetch and load
    (async () => {
      try {
        const resp = await projectAPI.getProject(pid);
        await loadExistingProject(resp.data);
      } catch (e) {
        console.warn("Failed to hydrate project from pid:", e);
      }
    })();
  }, [searchParams, isEditMode, projectInfo?.id, loadExistingProject]);

  // Sticky sidebar positioning with JavaScript
  useEffect(() => {
    const sidebar = sidebarRef.current;
    if (!sidebar) return;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;
      const sidebarHeight = sidebar.offsetHeight;
      const headerOffset = 48; // Account for any header/padding

      // Calculate the maximum scroll position where sidebar should stick to bottom
      const maxStickyTop = Math.max(0, viewportHeight - sidebarHeight - headerOffset);

      // Apply sticky positioning
      if (scrollTop <= maxStickyTop) {
        // Stick to top when there's enough space
        sidebar.style.position = 'sticky';
        sidebar.style.top = `${headerOffset}px`;
        sidebar.style.bottom = 'auto';
      } else {
        // Stick to bottom when sidebar is taller than viewport
        sidebar.style.position = 'sticky';
        sidebar.style.top = 'auto';
        sidebar.style.bottom = `${headerOffset}px`;
      }
    };

    // Initial positioning
    handleScroll();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  useEffect(() => {
    // Skip navigation validation entirely in edit mode or while loading project data
    if (isEditMode || isLoadingProject) {
      return;
    }

    // Always allow access to Analytics Services step (OAuth return landing)
    // This page must not be blocked by step guards in create mode
    if (pathname && pathname.includes("/create-project/analytics-services")) {
      return;
    }

    // Enhanced navigation validation using the store's canAccessStep method
    const isStepAccessible = (stepUrl: string): boolean => {
      // Use the store's enhanced validation logic
      return canAccessStep(stepUrl);
    };

    // Only perform navigation validation in create mode (excluding analytics-services)
    if (pathname && !isStepAccessible(pathname)) {
      // Use enhanced redirect logic based on step completion with proper URL building
      if (!canAccessStep("/create-project/search-engines")) {
        router.replace(urls.projectInformation);
      } else if (!canAccessStep("/create-project/keywords")) {
        router.replace(urls.searchEngines);
      } else if (!canAccessStep("/create-project/competitors")) {
        router.replace(urls.keywords);
      } else if (!canAccessStep("/create-project/analytics-services")) {
        router.replace(urls.competitors);
      }
    }
  }, [
    pathname,
    projectInfo,
    searchEngineConfigs,
    keywords,
    router,
    isEditMode,
    isLoadingProject,
    canAccessStep,
    urls,
    stepCompletion,
    currentStep,
  ]);

  // Show loading state while fetching project data in edit mode
  if (isEditMode && isLoadingProject) {
    return <LayoutLoading />;
  }

  // Show error state if project failed to load
  if (isEditMode && projectError) {
    return (
      <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-lg font-semibold">
            Failed to load project data
          </div>
          <div className="text-gray-600">
            Please try refreshing the page or go back to the projects list.
          </div>
          <button
            onClick={() => router.push("/dashboard/my-projects")}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Back to Projects
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex flex-col lg:flex-row min-h-screen gap-6 lg:gap-8">
        <div
          ref={sidebarRef}
          className="hidden lg:block lg:w-80 xl:w-96 flex-shrink-0 lg:self-start"
        >
          <SidebarCreateProject />
        </div>
        <div className="flex-1 min-w-0">{children}</div>
      </div>
      <ToastProvider />
    </>
  );
}

// Loading fallback component
function LayoutLoading() {
  return (
    <div className="w-full max-w-8xl mx-auto px-4 xl:px-2 lg:px-6 pt-6 lg:pt-12 pb-6 lg:pb-8 flex flex-col lg:flex-row min-h-screen gap-6 lg:gap-8">
      <div className="hidden lg:block lg:w-80 xl:w-96 flex-shrink-0 lg:self-start">
        <div className="animate-pulse bg-gray-200 rounded-lg h-96"></div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="animate-pulse bg-gray-200 rounded-lg h-96"></div>
      </div>
    </div>
  );
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={<LayoutLoading />}>
      <LayoutContent>{children}</LayoutContent>
    </Suspense>
  );
}
