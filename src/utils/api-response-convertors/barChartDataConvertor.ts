import { CompetitorApiResponse } from "./lineChartDataConvertor";

type BarChartDataResponse = {
  cardsData: Record<
    string,
    {
      amount: number;
      growth: string;
    }
  >;
  colors: { name: string; color: string }[];
  barChartData: { name: string; bar: number }[];
};

export function barChartDataConvertor(
  inputData: CompetitorApiResponse,
  title: string,
  themeColor?: string
): BarChartDataResponse {
  const barChartData = inputData.competitors.map((c) => ({
    name: c.domain,
    bar: Math.round(c.totals[title] ?? 0),
  }));

  const colors = inputData.competitors.map((c) => ({
    name: c.domain,
    color: themeColor ?? "#914ac4",
  }));

  // Step 1: build cardsData
  const cardsData: Record<string, { amount: number; growth: string }> = {};
  inputData.competitors.forEach((c) => {
    const key = c.domain.replace(/\./g, "_");
    const value = Math.round(c.totals[title] ?? 0);
    cardsData[key] = {
      amount: Math.round(c.totals[title] ?? 0),
      growth: `${value > 0 ? "+" : ""}${value}%`,
    };
  });

  // Step 2: reorder cardsData if needed (move first key to end)
  const keys = Object.keys(cardsData);
  if (keys.length > 1) {
    const firstKey = keys.shift()!;
    keys.push(firstKey);
  }

  const orderedCardsData: Record<string, { amount: number; growth: string }> =
    {};
  keys.forEach((key) => {
    orderedCardsData[key] = cardsData[key];
  });

  return {
    cardsData: orderedCardsData,
    colors,
    barChartData,
  };
}
